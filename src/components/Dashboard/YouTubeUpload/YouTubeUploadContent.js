import { useState, useCallback, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Link } from "@/components/Common/Link";
import { useTranslations } from "next-intl";
import { transcriptionService } from "@/services/api/transcriptionService";
import { toolsService } from "@/services/api/toolsService";
import { TranscriptionLimitDisplay } from "@/components/Dashboard/TranscriptionLimitDisplay";
import { checkTranscriptionLimit } from "@/lib/transcriptionUtils";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";

import { trackEvent } from "@/lib/analytics";
import isValidYouTubeUrl from "@/lib/youtube";
import { useTranscriptionSettings } from "@/hooks/useTranscriptionSettings";
import TranscriptionSettings from "@/components/Common/TranscriptionSettings/TranscriptionSettings";
import { FEATURES } from "@/config/features";

/**
 * YouTube 上传核心内容组件
 * 可在对话框和页面中复用
 */
const YouTubeUploadContent = forwardRef(
  (
    { onTranscribeSubmit, selectedFolderId = null, showLimitDisplay = true },
    ref
  ) => {
    const t = useTranslations("dashboard");
    const tCommon = useTranslations("common");

    const [url, setUrl] = useState("");
    const [videoData, setVideoData] = useState(null);
    const [isSearchLoading, setIsSearchLoading] = useState(false);
    const [isTranscribeLoading, setIsTranscribeLoading] = useState(false);
    const [error, setError] = useState(null);
    const [showTranscriptionLimitAlert, setShowTranscriptionLimitAlert] =
      useState(false);
    const { summary } = useEntitlementsStore();

    // 使用统一的转录设置Hook
    const {
      selectedLanguage,
      subtitleEnabled,
      enableSpeakerDiarization,
      advancedSettingsOpen,
      showPremiumDialog,
      handleLanguageSelect,
      handleSubtitleChange,
      handleSpeakerDiarizationChange,
      handleAdvancedSettingsToggle,
      setShowPremiumDialog,
    } = useTranscriptionSettings();

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      reset: () => {
        setUrl("");
        setVideoData(null);
        setError(null);
        setIsSearchLoading(false);
        setIsTranscribeLoading(false);
        setShowTranscriptionLimitAlert(false);
      },
      getState: () => ({
        url,
        videoData,
        isLoading: isSearchLoading || isTranscribeLoading,
      }),
    }));

    const handleUrlChange = useCallback((e) => {
      if (e.nativeEvent.inputType === "insertFromPaste") return;

      const newValue = e.target.value;
      setUrl(newValue);

      if (!newValue) {
        setVideoData(null);
        setError(null);
        setIsSearchLoading(false);
        setIsTranscribeLoading(false);
      }
    }, []);

    const handlePaste = useCallback(async (e) => {
      const pastedUrl = e.clipboardData.getData("text");
      if (!pastedUrl) return;

      setUrl(pastedUrl);

      // 检查YouTube升级状态
      if (FEATURES.YOUTUBE_UPGRADE.enabled) {
        setError(t("upgradeNotice"));
        return;
      }

      await handleSearch(pastedUrl);
    }, []);

    const handleSearch = useCallback(
      async (searchUrl = url) => {
        if (!searchUrl) return;

        // 检查YouTube升级状态
        if (FEATURES.YOUTUBE_UPGRADE.enabled) {
          setError(t("upgradeNotice"));
          return;
        }

        setIsSearchLoading(true);
        setError(null);
        setVideoData(null);

        try {
          const validation = isValidYouTubeUrl(searchUrl);
          if (!validation.isValid) {
            if (validation.reason === "Invalid YouTube video URL") {
              trackEvent("youtube_search_error", {
                url: searchUrl,
                error: "Invalid YouTube video URL",
                source: "youtube_upload_dialog",
              });
            }
            throw new Error(validation.reason);
          }

          const urlToUse = validation.extractedUrl || searchUrl;
          const data = await toolsService.getYoutubeInfo(urlToUse);
          setVideoData(data);
          trackEvent("youtube_upload.youtube_search_success", {});
        } catch (err) {
          console.error("YouTube search error:", err);
          setError(err.message || tCommon("errors.network"));
          trackEvent("youtube_search_error", {
            url: searchUrl,
            error: err.message,
            source: "youtube_upload_dialog",
          });
        } finally {
          setIsSearchLoading(false);
        }
      },
      [url, tCommon]
    );

    const handleTranscribe = async () => {
      if (!url || !videoData) return;

      trackEvent("youtube_upload.youtube_transcribe_start", {});

      if (!videoData.duration || isNaN(videoData.duration)) {
        setError("Invalid video duration");
        trackEvent("youtube_transcribe_error", {
          error: "Invalid video duration",
        });
        return;
      }

      if (!checkTranscriptionLimit(videoData.duration, summary, "youtube")) {
        setShowTranscriptionLimitAlert(true);
        trackEvent("youtube_transcribe_limit_exceeded", {
          duration: videoData.duration,
          currentUsage: summary,
        });
        setTimeout(() => {
          setShowTranscriptionLimitAlert(false);
        }, 3000);
        return;
      }

      setIsTranscribeLoading(true);
      setError(null);

      try {
        // Check if we need to extract a nested URL
        const validation = isValidYouTubeUrl(url);
        const urlToUse = validation.isValid
          ? validation.extractedUrl || url
          : url;

        const response =
          await transcriptionService.createYoutubeTranscriptionTask(
            urlToUse,
            videoData.title,
            videoData.duration,
            subtitleEnabled,
            selectedLanguage,
            enableSpeakerDiarization,
            selectedFolderId
          );

        if (response.status === 200) {
          trackEvent("youtube_upload.youtube_transcribe_success", {
            duration: videoData.duration,
            language: selectedLanguage,
            subtitle: subtitleEnabled,
            enableSpeakerDiarization: enableSpeakerDiarization,
          });

          if (onTranscribeSubmit) {
            onTranscribeSubmit(response.data);
          }
        }
      } catch (error) {
        console.error("YouTube transcription error:", error);

        let errorMsg = "An error occurred";

        // 特殊处理403错误（文件夹权限错误）
        if (error.response?.status === 403) {
          errorMsg =
            error.response?.data?.message ||
            tCommon("fileUploadStatus.error.invalidFolder");
        } else if (error.response?.data?.message) {
          errorMsg = error.response.data.message;
        } else if (error.message) {
          errorMsg = error.message;
        }

        setError(errorMsg);

        trackEvent("youtube_transcribe_error", {
          error: errorMsg,
          status: error.response?.status,
          duration: videoData?.duration,
        });
      } finally {
        setIsTranscribeLoading(false);
      }
    };

    return (
      <div className="space-y-6">
        {showTranscriptionLimitAlert && (
          <Alert variant="destructive">
            <AlertDescription>
              {t("youtube_upload.alerts.transcription_limit")}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col sm:flex-row gap-4">
          <Input
            placeholder={t("youtube_upload.placeholder")}
            value={url}
            onChange={handleUrlChange}
            onPaste={handlePaste}
            className="flex-1 h-10 border-custom-bg focus-visible:ring-custom-bg/20 focus-visible:ring-offset-0"
            aria-label="YouTube Link input"
          />
          <Button
            type="button"
            onClick={handleSearch}
            disabled={!url || isSearchLoading || isTranscribeLoading}
            variant="outline"
            className="h-10 px-6 border-custom-bg text-custom-bg hover:bg-custom-bg/5 transition-colors"
            aria-label="Search video"
          >
            {isSearchLoading
              ? t("youtube_upload.searching")
              : t("youtube_upload.search")}
          </Button>
        </div>

        {error && (
          <p
            className={`text-sm font-medium ${
              error === t("upgradeNotice")
                ? "text-orange-600"
                : "text-destructive"
            }`}
            role="alert"
          >
            {error}
          </p>
        )}

        {videoData && (
          <div className="flex flex-col gap-4 mt-6">
            <h3 className="text-lg font-semibold text-foreground text-center">
              {videoData.title}
            </h3>
            <div className="max-w-[400px] mx-auto w-full aspect-video rounded-lg overflow-hidden bg-muted">
              <img
                src={videoData.thumbnailUrl}
                alt={videoData.title}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>

            <div className="max-w-[400px] mx-auto w-full space-y-4">
              <TranscriptionSettings
                selectedLanguage={selectedLanguage}
                subtitleEnabled={subtitleEnabled}
                enableSpeakerDiarization={enableSpeakerDiarization}
                advancedSettingsOpen={advancedSettingsOpen}
                showPremiumDialog={showPremiumDialog}
                onLanguageSelect={handleLanguageSelect}
                onSubtitleChange={handleSubtitleChange}
                onSpeakerDiarizationChange={handleSpeakerDiarizationChange}
                onAdvancedSettingsToggle={handleAdvancedSettingsToggle}
                onPremiumDialogClose={() => setShowPremiumDialog(false)}
                containerClassName="space-y-4"
              />

              <Button
                type="button"
                onClick={handleTranscribe}
                disabled={!videoData || isTranscribeLoading || isSearchLoading}
                className="w-full h-10 px-6 text-white bg-custom-bg hover:bg-custom-bg/90 transition-colors"
                aria-label="Transcribe video"
              >
                {isTranscribeLoading
                  ? t("youtube_upload.processing")
                  : t("youtube_upload.transcribe")}
              </Button>
            </div>

            <Link
              href={`/tools/youtube-video-downloader?url=${encodeURIComponent(
                url
              )}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-custom-bg hover:text-custom-bg/90 text-sm text-center font-medium transition-colors"
            >
              {t("youtube_upload.download_video")}
            </Link>
          </div>
        )}

        {showLimitDisplay && <TranscriptionLimitDisplay />}
      </div>
    );
  }
);

YouTubeUploadContent.displayName = "YouTubeUploadContent";

export default YouTubeUploadContent;
