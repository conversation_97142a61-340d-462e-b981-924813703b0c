# YouTube转录服务升级提示功能

## 功能概述

这个功能允许通过全局配置控制YouTube转录服务的升级提示显示。当开启配置后，用户在尝试使用YouTube转录功能时会看到升级提示信息，而不是执行实际的搜索操作。

## 实现位置

### 1. 全局配置文件
- **文件**: `src/config/features.js`
- **配置项**: `YOUTUBE_UPGRADE`
  ```javascript
  YOUTUBE_UPGRADE: {
    enabled: false,  // 控制是否显示升级提示
    message: "Sorry for the interruption! YouTube transcription is being upgraded and will return shortly.",
  }
  ```

### 2. 国际化文案
- **英文**: `src/messages/en/transcriptionBox.json` 和 `src/messages/en/dashboard.json`
- **中文**: `src/messages/zh/transcriptionBox.json` 和 `src/messages/zh/dashboard.json`
- **文案键**: `upgradeNotice`

### 3. 受影响的组件

#### 首页YouTube标签页
- **文件**: `src/components/Home/Features/TranscriptionTabs/YouTubeTab.js`
- **功能**: 在粘贴YouTube地址或点击搜索按钮时检查升级状态

#### 首页转录框
- **文件**: `src/components/Home/Features/TranscriptionBox.js`
- **功能**: 在handleSearch函数中检查升级状态

#### Dashboard YouTube上传对话框
- **文件**: `src/components/Dashboard/YouTubeUpload/YouTubeUploadContent.js`
- **功能**: 在粘贴YouTube地址或点击搜索按钮时检查升级状态

## 使用方法

### 开启升级提示
1. 打开 `src/config/features.js` 文件
2. 将 `YOUTUBE_UPGRADE.enabled` 设置为 `true`
3. 重新部署应用

### 关闭升级提示
1. 打开 `src/config/features.js` 文件
2. 将 `YOUTUBE_UPGRADE.enabled` 设置为 `false`
3. 重新部署应用

## 功能效果

### 开启状态下的行为
1. **首页YouTube标签页**:
   - 粘贴YouTube链接时显示升级提示，不执行搜索
   - 点击搜索按钮时显示升级提示，不执行搜索
   - 提示信息以橙色文字显示

2. **Dashboard YouTube上传对话框**:
   - 粘贴YouTube链接时显示升级提示，不执行搜索
   - 点击搜索按钮时显示升级提示，不执行搜索
   - 提示信息以橙色文字显示

### 关闭状态下的行为
- 所有YouTube转录功能正常工作
- 不显示任何升级提示

## 提示信息样式
- **颜色**: 橙色 (`text-orange-600`)
- **字体**: 小号粗体 (`text-sm font-medium`)
- **位置**: 搜索框下方居中显示

## 注意事项
1. 配置更改需要重新部署才能生效
2. 升级提示会完全阻止YouTube搜索功能的执行
3. 提示信息支持多语言（目前支持英文和中文）
4. 升级提示与普通错误信息使用不同的颜色样式进行区分
